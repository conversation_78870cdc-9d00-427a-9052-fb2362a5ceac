package com.snct.typhoon.task;

import com.snct.typhoon.service.TyphoonService;
import com.snct.typhoon.service.TyphoonWarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName: TyphoonWarningScheduler
 * @Description: 台风预警定时任务调度器
 * @author: wzewei
 * @date: 2025-08-22 11:41
 */
@Component
@ConditionalOnProperty(name = "typhoon.warning.scheduler-enabled", havingValue = "true", matchIfMissing = true)
public class TyphoonWarningScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningScheduler.class);
    
    @Autowired
    private TyphoonWarningService typhoonWarningService;

    @Autowired
    private TyphoonService typhoonService;
    
    /**
     * 定时台风检查
     * 每一小时执行一次
     */
    @Scheduled(fixedRate = 60*60*1000)
    public void scheduledTyphoonWarningCheck() {
        logger.info("开始执行定时台风检查");
        
        long startTime = System.currentTimeMillis();
        
        try {

            // 获取台风信息(获取最新台风信息并预警检查)
            typhoonService.obtainTyphoon();

            //typhoonWarningService.checkAndSaveTyphoonWarnings();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("定时台风检查完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("定时台风检查失败，耗时: {}ms", duration, e);
        }
    }
    
    /**
     * 定时清理过期预警记录
     * 每天凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void scheduledCleanExpiredWarnings() {
        logger.info("开始执行定时清理过期预警记录");
        
        long startTime = System.currentTimeMillis();
        
        try {
            typhoonWarningService.cleanExpiredWarnings();
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("定时清理过期预警记录完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error("定时清理过期预警记录失败，耗时: {}ms", duration, e);
        }
    }
}
